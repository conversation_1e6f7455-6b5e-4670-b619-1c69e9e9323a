export interface TagType {
    tagID?: string; // Optional for creation, present in responses
    name: string;
    createdAt?: string; // Optional for creation
}

export interface TagStats {
    name: string;
    count: number;
    uniqueBlinks: number;
    createdAt: string;
}

export interface TagSearchResponse {
    success: boolean;
    status: number;
    message: string;
    data: {
        tags: TagType[];
        query: string;
    };
}

export interface TagStatsResponse {
    success: boolean;
    status: number;
    message: string;
    data: {
        tags: TagStats[];
        timeFilter: string;
        message: string;
    };
}

export interface TagTrendingResponse {
    success: boolean;
    status: number;
    message: string;
    data: {
        trending: {
            last24h: TagStats[];
            last7days: TagStats[];
            last30days: TagStats[];
            allTime: TagStats[];
        };
    };
}

export interface TagValidationResponse {
    success: boolean;
    status: number;
    message: string;
    data: {
        valid: boolean;
        tags: string[];
        error?: string;
    };
}

export interface TagBlinksResponse {
    success: boolean;
    status: number;
    message: string;
    data: {
        blinkIDs: string[];
        tags: string[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
    };
}
