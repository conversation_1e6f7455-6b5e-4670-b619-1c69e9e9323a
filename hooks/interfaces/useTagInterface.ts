import { useFetchQuery } from "@/hooks/repository/useFetchQuery";
import { usePostMutation } from "@/hooks/repository/usePostMutation";
import { usePaginatedQuery } from "@/hooks/usePaginatedQuery";
import { 
    TagSearchResponse, 
    TagStatsResponse, 
    TagTrendingResponse, 
    TagValidationResponse,
    TagBlinksResponse 
} from "@/types/TagsType";
import { useQuery } from "@tanstack/react-query";

// Hook pour rechercher des tags (autocomplétion)
export function useTagSearchQuery(query: string, limit: number = 10) {
    return useQuery({
        queryKey: ["tags", "search", query, limit],
        queryFn: async () => {
            const response = await fetch(
                `${process.env.EXPO_PUBLIC_API_URL}/tags/search?q=${encodeURIComponent(query)}&limit=${limit}`
            );
            
            if (!response.ok) {
                throw new Error('Failed to search tags');
            }
            
            return response.json() as Promise<TagSearchResponse>;
        },
        enabled: query.length > 0, // Ne lance la requête que si on a une query
        staleTime: 30000, // Cache pendant 30 secondes
    });
}

// Hook pour récupérer les tags populaires
export function usePopularTagsQuery(timeFilter: '24h' | '7d' | '30d' | 'all' = 'all', limit: number = 20) {
    return useFetchQuery<TagStatsResponse>(
        `/tags/popular?timeFilter=${timeFilter}&limit=${limit}`,
        ["tags", "popular", timeFilter, limit]
    );
}

// Hook pour récupérer les tags tendances
export function useTrendingTagsQuery() {
    return useFetchQuery<TagTrendingResponse>(
        `/tags/trending`,
        ["tags", "trending"]
    );
}

// Hook pour valider des tags
export function useValidateTagsMutation() {
    return usePostMutation<TagValidationResponse>(`/tags/validate`);
}

// Hook pour récupérer les blinks par tags
export function useBlinksByTagsQuery(tagNames: string[], page: number = 1, limit: number = 10) {
    const tagsParam = tagNames.join(',');
    
    return useQuery({
        queryKey: ["tags", "blinks", tagNames, page, limit],
        queryFn: async () => {
            const response = await fetch(
                `${process.env.EXPO_PUBLIC_API_URL}/tags/${encodeURIComponent(tagsParam)}/blinks?page=${page}&limit=${limit}`
            );
            
            if (!response.ok) {
                throw new Error('Failed to fetch blinks by tags');
            }
            
            return response.json() as Promise<TagBlinksResponse>;
        },
        enabled: tagNames.length > 0,
    });
}

// Hook pour récupérer les statistiques des tags
export function useTagStatsQuery(timeFilter: '24h' | '7d' | '30d' | 'all' = 'all', limit: number = 50) {
    return useFetchQuery<TagStatsResponse>(
        `/tags/stats?timeFilter=${timeFilter}&limit=${limit}`,
        ["tags", "stats", timeFilter, limit]
    );
}
