{"name": "blinker", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@react-navigation/native-stack": "^7.2.0", "@tanstack/react-query": "^5.66.3", "async-storage": "^0.1.0", "expo": "^53.0.0", "expo-app-loading": "^1.0.3", "expo-auth-session": "~6.2.0", "expo-blur": "~14.1.5", "expo-constants": "~17.1.6", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image-picker": "^16.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.5", "expo-localization": "~16.1.5", "expo-modules-core": "^2.2.2", "expo-router": "~5.1.0", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.8", "expo-video": "~2.2.1", "expo-web-browser": "~14.1.6", "i18next": "^24.2.2", "i18next-browser-languagedetector": "^8.0.3", "i18next-http-backend": "^3.0.2", "metro": "^0.82.0", "npm-check-updates": "^17.1.14", "react": "19.0.0", "react-dom": "19.0.0", "react-i18next": "^15.4.0", "react-native": "0.79.3", "react-native-devsettings": "^1.0.5", "react-native-dotenv": "^3.4.11", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "socket.io-client": "^4.8.1", "uuid": "^11.1.0"}, "devDependencies": {"@babel/core": "^7.26.9", "@types/jest": "^29.5.14", "@types/react": "~19.0.10", "@types/react-dom": "^19.1.6", "@types/react-native-dotenv": "^0.2.2", "@types/react-test-renderer": "18.3.1", "@types/uuid": "^10.0.0", "jest": "^29.7.0", "jest-expo": "~53.0.7", "react-test-renderer": "18.3.1", "typescript": "~5.8.3"}, "private": true}